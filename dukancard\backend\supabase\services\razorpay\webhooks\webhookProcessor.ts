/**
 * CENTRALIZED WEBHOOK PROCESSOR 
 * 
 * This module provides a unified, race-condition-safe webhook processing system
 * that ensures consistent subscription state management across all webhook events.
 */

import { SupabaseClient } from "@supabase/supabase-js";
import { createAdminClient } from "@/utils/supabase/admin";
import {
  type SubscriptionStatus
} from "./utils";
import { WebhookProcessingContext } from "./core/types";
export type { WebhookProcessingContext };
import { subscriptionManager } from "./core/subscriptionManager";
import { eventManager } from "./core/eventManager";
import { eventOrderValidator } from "./core/validation/eventOrderValidator";

/**
 * Centralized webhook processor that handles all subscription webhook events
 * with proper race condition protection and state validation.
 */
export class WebhookProcessor {
  private adminClient: SupabaseClient;

  constructor() {
    this.adminClient = createAdminClient();
  }

  /**
   * Process a webhook event with proper state validation and race condition protection
   */
  async processWebhookEvent(context: WebhookProcessingContext): Promise<{
    success: boolean;
    message: string;
    shouldProcess: boolean;
  }> {
    try {
      // 1. Check if event has already been processed (idempotency)
      const isAlreadyProcessed = await eventManager.checkEventIdempotency(context.eventId);
      if (isAlreadyProcessed) {
        return {
          success: true,
          message: "Event already processed",
          shouldProcess: false
        };
      }

      // 2. Get current subscription state
      let currentState = await subscriptionManager.getCurrentSubscriptionState(context.subscriptionId);
      if (!currentState) {
        console.warn(`[WEBHOOK_PROCESSOR] Subscription ${context.subscriptionId} not found in database. This could be a race condition or the subscription was created outside our system.`);

        if (this.shouldCreateMissingSubscription(context.eventType)) {
          const createdState = await subscriptionManager.createMissingSubscriptionRecord(context);
          if (!createdState) {
            console.error(`[WEBHOOK_PROCESSOR] Failed to create missing subscription record for ${context.subscriptionId}`);
            return {
              success: false,
              message: "Subscription not found and could not be created",
              shouldProcess: false
            };
          }
          currentState = createdState;
        } else {
          return {
            success: true,
            message: `Subscription not found for event ${context.eventType} - this may be expected`,
            shouldProcess: false
          };
        }
      }

      // 3. Validate webhook event ordering and whether it should be processed
      if (currentState) {
        const orderValidation = await eventOrderValidator.validateWebhookEventOrder(context, currentState);
        if (!orderValidation.shouldProcess) {
          console.warn(`[WEBHOOK_PROCESSOR] Rejecting out-of-order webhook: ${orderValidation.reason}`);
          return {
            success: true,
            message: `Webhook rejected: ${orderValidation.reason}`,
            shouldProcess: false
          };
        }

        const shouldProcess = await eventOrderValidator.shouldProcessEvent(context, currentState);
        if (!shouldProcess.should) {
          return {
            success: true,
            message: shouldProcess.reason,
            shouldProcess: false
          };
        }
      }

      // 4. Mark event as being processed
      await eventManager.markEventAsProcessing(context.eventId, context.eventType);

      return {
        success: true,
        message: "Event validated and ready for processing",
        shouldProcess: true
      };

    } catch (error) {
      console.error(`[WEBHOOK_PROCESSOR] Error processing webhook event ${context.eventId}:`, error);
      return {
        success: false,
        message: `Error processing webhook: ${error instanceof Error ? error.message : String(error)}`,
        shouldProcess: false
      };
    }
  }

  /**
   * Determine if we should create a missing subscription record for this event type
   */
  private shouldCreateMissingSubscription(eventType: string): boolean {
    const createForEvents = [
      'subscription.authenticated',
      'subscription.activated',
      'subscription.charged',
      'subscription.halted',
      'subscription.cancelled'
    ];

    return createForEvents.includes(eventType);
  }

  /**
   * Mark an event as successfully processed
   */
  async markEventAsSuccess(eventId: string, message: string = "Successfully processed"): Promise<void> {
    await eventManager.markEventAsSuccess(eventId, message);
  }

  /**
   * Mark an event as failed
   */
  async markEventAsFailed(eventId: string, errorMessage: string): Promise<void> {
    await eventManager.markEventAsFailed(eventId, errorMessage);
  }

  /**
   * ENHANCED: Update subscription status with atomic transaction safety
   */
  async updateSubscriptionStatus(
    subscriptionId: string,
    newStatus: SubscriptionStatus,
    additionalData: Record<string, unknown> = {},
    webhookTimestamp?: number
  ): Promise<{ success: boolean; message: string }> {
    return await subscriptionManager.updateSubscriptionStatus(subscriptionId, newStatus, additionalData, webhookTimestamp);
  }
}

// Export singleton instance
export const webhookProcessor = new WebhookProcessor();
