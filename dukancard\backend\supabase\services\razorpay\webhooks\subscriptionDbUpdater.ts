import { SupabaseClient } from "@supabase/supabase-js";
import { SupabaseSubscriptionStatus } from "../types";
import { createAdminClient } from "@/utils/supabase/admin";
import { SubscriptionStateManager } from "./subscription-state-manager";

/**
 * CENTRALIZED SUBSCRIPTION UPDATE FUNCTION
 * 
 * This function implements the single source of truth for subscription status handling.
 * All subscription updates must go through this function to ensure consistency.
 * 
 * @param supabase The Supabase client (admin client will be used internally)
 * @param subscriptionId The Razorpay subscription ID
 * @param status The new subscription status
 * @param additionalData Additional data to update
 * @returns The result of the update operation
 */
export async function updateSubscription(
  _supabase: SupabaseClient, // Original client not used, using admin client instead
  subscriptionId: string,
  status: SupabaseSubscriptionStatus,
  additionalData: Record<string, unknown> = {}
): Promise<{ success: boolean; message: string }> {
  try {
    // Get admin client to bypass RLS
    const adminClient = createAdminClient();

    // Get subscription details from Razorpay to find the business_profile_id and other details
    const { getSubscription } = await import('@/lib/razorpay/services/subscription');
    const subscriptionDetails = await getSubscription(subscriptionId);

    if (!subscriptionDetails.success || !subscriptionDetails.data) {
      console.error(`[RAZORPAY_WEBHOOK] Failed to get subscription details from Razorpay for ${subscriptionId}`);

      // ENHANCED: Try to get business_profile_id from our database instead
      const { data: localSubscription, error: localError } = await adminClient
        .from('payment_subscriptions')
        .select('business_profile_id, plan_id, plan_cycle')
        .eq('razorpay_subscription_id', subscriptionId)
        .maybeSingle();

      if (localError || !localSubscription) {
        console.error(`[RAZORPAY_WEBHOOK] Also failed to get subscription from local database for ${subscriptionId}:`, localError);
        return { success: false, message: `Failed to get subscription details from both Razorpay and local database` };
      }

      console.log(`[RAZORPAY_WEBHOOK] Using local subscription data for ${subscriptionId} since Razorpay API failed`);

      // Create a mock subscription details object using local data
      const mockSubscriptionDetails = {
        success: true,
        data: {
          id: subscriptionId,
          plan_id: `${localSubscription.plan_id}_${localSubscription.plan_cycle}`,
          customer_id: null,
          current_start: null,
          current_end: null,
          charge_at: null,
          start_at: null,
          notes: {
            business_profile_id: localSubscription.business_profile_id,
            plan_type: localSubscription.plan_id,
            plan_cycle: localSubscription.plan_cycle
          }
        }
      };

      // Use the mock data for the rest of the function
      const subscriptionDetailsToUse = mockSubscriptionDetails;
      return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetailsToUse.data);
    }

    // Continue with normal processing using Razorpay data
    return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetails.data);
  } catch (error) {
    console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);
    return {
      success: false,
      message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Process subscription update with given subscription data
 * This function contains the main logic extracted from updateSubscription
 */
async function processSubscriptionUpdate(
  adminClient: SupabaseClient,
  subscriptionId: string,
  status: SupabaseSubscriptionStatus,
  additionalData: Record<string, unknown>,
  subscriptionData: {
    id: string;
    plan_id: string;
    customer_id: string | null;
    current_start: number | null;
    current_end: number | null;
    charge_at: number | null;
    start_at: number | null;
    notes: {
      business_profile_id?: string;
      user_id?: string;
      plan_type?: string;
      plan_cycle?: string;
    };
  }
): Promise<{ success: boolean; message: string }> {
  try {
    // Extract business_profile_id from notes
    const businessProfileId = subscriptionData.notes?.business_profile_id ||
                             subscriptionData.notes?.user_id;

    if (!businessProfileId) {
      console.error(`[RAZORPAY_WEBHOOK] No business_profile_id found in subscription notes for ${subscriptionId}`);
      return { success: false, message: `No business_profile_id found in subscription notes` };
    }

    // Extract plan details from notes or plan_id
    let planType = subscriptionData.notes?.plan_type;
    let planCycle = subscriptionData.notes?.plan_cycle;

    // If plan type and cycle are not in notes, try to determine from plan_id
    if (!planType || !planCycle) {
      console.log(`[RAZORPAY_WEBHOOK] Plan type or cycle not found in notes, determining from plan_id: ${subscriptionData.plan_id}`);

      // Use centralized plan configuration to map Razorpay plan ID to plan details
      const { getPlanByRazorpayPlanId } = await import('@/lib/config/plans');
      const planDetails = getPlanByRazorpayPlanId(subscriptionData.plan_id);

      if (planDetails) {
        planType = planDetails.id;
        // Determine cycle by checking which Razorpay plan ID matches
        planCycle = planDetails.razorpayPlanIds.monthly === subscriptionData.plan_id ? "monthly" : "yearly";
        console.log(`[RAZORPAY_WEBHOOK] Determined plan type: ${planType}, cycle: ${planCycle} from plan_id using centralized config`);
      } else {
        // Default to basic monthly if we can't determine
        planType = "basic";
        planCycle = "monthly";
        console.log(`[RAZORPAY_WEBHOOK] Could not determine plan type and cycle from plan_id: ${subscriptionData.plan_id}, defaulting to basic monthly`);
      }
    }

    // CENTRALIZED LOGIC: Use SubscriptionStateManager to determine has_active_subscription
    const hasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(status, planType || 'free');

    // Create a copy of additionalData to avoid modifying the original
    const additionalDataCopy = { ...additionalData };

    // Remove has_active_subscription from additionalData if it exists
    // This ensures we always set it based on the status, not what's passed in
    if ('has_active_subscription' in additionalDataCopy) {
      console.log(`[RAZORPAY_WEBHOOK] Removing has_active_subscription from additionalData for subscription with status ${status}`);
      delete additionalDataCopy.has_active_subscription;
    }

    // Extract subscription dates from Razorpay subscription details
    let subscriptionStartDate = subscriptionData.current_start
      ? new Date(subscriptionData.current_start * 1000).toISOString()
      : null;

    const subscriptionExpiryTime = subscriptionData.current_end
      ? new Date(subscriptionData.current_end * 1000).toISOString()
      : null;

    const subscriptionChargeTime = subscriptionData.charge_at
      ? new Date(subscriptionData.charge_at * 1000).toISOString()
      : null;

    // Check if the status is authenticated or active
    const isValidStatus = status === SupabaseSubscriptionStatus._AUTHENTICATED ||
                         status === SupabaseSubscriptionStatus._ACTIVE;

    if (!isValidStatus) {
      console.log(`[RAZORPAY_WEBHOOK] Skipping creation/update of subscription record for ${subscriptionId} with status ${status} - only handling authenticated or active statuses`);
      return { success: true, message: `Skipped creation/update of subscription record with status ${status}` };
    }

    // For authenticated subscriptions, ensure we're setting the correct dates
    // This is especially important for trial users where start_at is in the future
    if (status === SupabaseSubscriptionStatus._AUTHENTICATED) {
      console.log(`[RAZORPAY_WEBHOOK] Authenticated subscription detected, ensuring dates are set correctly`);

      // For authenticated subscriptions, we need to check if start_at is set
      // If it is, we should use that for subscription_start_date instead of current_start
      if (subscriptionData.start_at) {
        const startAt = new Date(subscriptionData.start_at * 1000).toISOString();
        console.log(`[RAZORPAY_WEBHOOK] Using start_at (${startAt}) for subscription_start_date`);

        // Override the subscription_start_date with start_at
        subscriptionStartDate = startAt;
      }
    }

    // Find existing subscription record
    const { data: subscription, error: findError } = await adminClient
      .from('payment_subscriptions')
      .select('id, business_profile_id, razorpay_subscription_id')
      .eq('razorpay_subscription_id', subscriptionId)
      .maybeSingle();

    if (findError) {
      console.error(`[RAZORPAY_WEBHOOK] Error finding subscription ${subscriptionId}:`, findError);
      return { success: false, message: `Error finding subscription: ${findError.message}` };
    }

    // If no subscription record exists for this Razorpay subscription ID, check if there's an existing record for the business
    if (!subscription) {
      console.log(`[RAZORPAY_WEBHOOK] No subscription found with ID ${subscriptionId}, checking for existing subscription for business ${businessProfileId}`);

      // Check if there's an existing subscription for this business
      const { data: existingSubscriptions, error: existingError } = await adminClient
        .from('payment_subscriptions')
        .select('id, razorpay_subscription_id, subscription_status')
        .eq('business_profile_id', businessProfileId);

      if (existingError) {
        console.error(`[RAZORPAY_WEBHOOK] Error checking for existing subscriptions for business ${businessProfileId}:`, existingError);
        return { success: false, message: `Error checking for existing subscriptions: ${existingError.message}` };
      }

      // If there's an existing subscription for this business, update it instead of creating a new one
      if (existingSubscriptions && existingSubscriptions.length > 0) {
        console.log(`[RAZORPAY_WEBHOOK] Found existing subscription for business ${businessProfileId}, updating instead of creating new one`);

        const existingSubscription = existingSubscriptions[0];

        // Create the update data object
        const updateData = {
          razorpay_subscription_id: subscriptionId,
          razorpay_customer_id: subscriptionData.customer_id || null,
          subscription_status: status,
          plan_id: planType,
          plan_cycle: planCycle,
          subscription_start_date: subscriptionStartDate,
          subscription_expiry_time: subscriptionExpiryTime,
          subscription_charge_time: subscriptionChargeTime,
          updated_at: new Date().toISOString(),
          ...additionalDataCopy
        };

        // Update the existing subscription record
        const { error: updateError } = await adminClient
          .from('payment_subscriptions')
          .update(updateData)
          .eq('id', existingSubscription.id);

        if (updateError) {
          console.error(`[RAZORPAY_WEBHOOK] Error updating existing subscription ${existingSubscription.id}:`, updateError);
          return { success: false, message: `Error updating existing subscription: ${updateError.message}` };
        }

        console.log(`[RAZORPAY_WEBHOOK] Updated existing subscription ${existingSubscription.id} with new Razorpay ID ${subscriptionId} and status ${status}`);

        // Use transaction utility to ensure consistency
        const transactionResult = await updateSubscriptionWithBusinessProfile({
          subscription_id: subscriptionId,
          business_profile_id: businessProfileId,
          subscription_status: status,
          has_active_subscription: hasActiveSubscription,
          additional_data: updateData
        });

        if (!transactionResult.success) {
          console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);
          return { success: false, message: `Transaction failed: ${transactionResult.message}` };
        }

        return { success: true, message: `Updated existing subscription with new Razorpay ID and status ${status}` };
      }

      // If no existing subscription for this business, create a new one
      console.log(`[RAZORPAY_WEBHOOK] No existing subscription found for business ${businessProfileId}, creating new one`);

      const insertData = {
        business_profile_id: businessProfileId,
        razorpay_subscription_id: subscriptionId,
        razorpay_customer_id: subscriptionData.customer_id || null,
        subscription_status: status,
        plan_id: planType,
        plan_cycle: planCycle,
        subscription_start_date: subscriptionStartDate,
        subscription_expiry_time: subscriptionExpiryTime,
        subscription_charge_time: subscriptionChargeTime,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...additionalDataCopy
      };

      const { data: _newSubscription, error: insertError } = await adminClient
        .from('payment_subscriptions')
        .insert(insertData)
        .select('id')
        .single();

      if (insertError) {
        console.error(`[RAZORPAY_WEBHOOK] Error creating subscription record for ${subscriptionId}:`, insertError);
        return { success: false, message: `Error creating subscription record: ${insertError.message}` };
      }

      console.log(`[RAZORPAY_WEBHOOK] Created new subscription record for ${subscriptionId}`);

      // Use transaction utility to ensure consistency
      const transactionResult = await updateSubscriptionWithBusinessProfile({
        subscription_id: subscriptionId,
        business_profile_id: businessProfileId,
        subscription_status: status,
        has_active_subscription: hasActiveSubscription,
        additional_data: insertData
      });

      if (!transactionResult.success) {
        console.error(`[RAZORPAY_WEBHOOK] Transaction failed for new subscription ${subscriptionId}:`, transactionResult.message);
        return { success: false, message: `Transaction failed: ${transactionResult.message}` };
      }

      console.log(`[RAZORPAY_WEBHOOK] Created subscription for ${subscriptionId} with status ${status}`);
      return { success: true, message: `Created subscription with status ${status}` };
    }

    // If subscription exists, update it
    const updateData = {
      subscription_status: status,
      subscription_start_date: subscriptionStartDate,
      subscription_expiry_time: subscriptionExpiryTime,
      subscription_charge_time: subscriptionChargeTime,
      updated_at: new Date().toISOString(),
      ...additionalDataCopy
    };

    // ENHANCED: Use atomic RPC function for transaction safety
    const transactionResult = await updateSubscriptionWithBusinessProfile({
      subscription_id: subscriptionId,
      business_profile_id: businessProfileId,
      subscription_status: status,
      has_active_subscription: hasActiveSubscription,
      additional_data: updateData
    });

    if (!transactionResult.success) {
      console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);
      return { success: false, message: `Transaction failed: ${transactionResult.message}` };
    }

    console.log(`[RAZORPAY_WEBHOOK] Updated subscription ${subscription.id} with status ${status}`);
    return { success: true, message: `Updated subscription with status ${status}` };
  } catch (error) {
    console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);
    return {
      success: false,
      message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * ATOMIC TRANSACTION UTILITY
 *
 * Ensures atomic updates between payment_subscriptions and business_profiles tables.
 * This prevents data inconsistencies that could cause users to lose access.
 */
interface TransactionParams {
  subscription_id: string;
  business_profile_id: string;
  subscription_status: string;
  has_active_subscription: boolean;
  additional_data?: Record<string, unknown>;
}

export async function updateSubscriptionWithBusinessProfile(
  params: TransactionParams
): Promise<{ success: boolean; message: string }> {
  try {
    const adminClient = createAdminClient();

    console.log(`[ATOMIC_TRANSACTION] Using atomic RPC for subscription ${params.subscription_id}`);

    // ENHANCED: Use atomic RPC function for true transaction safety
    const { data: result, error } = await adminClient.rpc('update_subscription_atomic', {
      p_subscription_id: params.subscription_id,
      p_new_status: params.subscription_status,
      p_business_profile_id: params.business_profile_id,
      p_has_active_subscription: params.has_active_subscription,
      p_additional_data: params.additional_data || {},
      p_webhook_timestamp: params.additional_data?.last_webhook_timestamp || null
    });

    if (error) {
      console.error(`[ATOMIC_TRANSACTION] RPC error for ${params.subscription_id}:`, error);
      return {
        success: false,
        message: `RPC error: ${error.message}`
      };
    }

    if (!result?.success) {
      console.error(`[ATOMIC_TRANSACTION] RPC function returned error for ${params.subscription_id}:`, result);
      return {
        success: false,
        message: result?.error || 'Unknown RPC error'
      };
    }

    console.log(`[ATOMIC_TRANSACTION] Successfully updated subscription ${params.subscription_id} atomically`);

    return { success: true, message: 'Atomic transaction completed successfully via RPC' };
  } catch (error) {
    console.error(`[ATOMIC_TRANSACTION] Exception in updateSubscriptionWithBusinessProfile:`, error);
    return {
      success: false,
      message: `Atomic transaction exception: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}