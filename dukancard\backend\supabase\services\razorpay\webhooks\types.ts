/**
 * Razorpay Subscription Webhook Event Types
 *
 * This file contains the types for Razorpay subscription webhook events.
 */

// Subscription webhook event types
export enum RazorpaySubscriptionEventType {
  // Subscription events
  _SUBSCRIPTION_AUTHENTICATED = "subscription.authenticated",
  _SUBSCRIPTION_ACTIVATED = "subscription.activated",
  _SUBSCRIPTION_CHARGED = "subscription.charged",
  _SUBSCRIPTION_PENDING = "subscription.pending",
  _SUBSCRIPTION_HALTED = "subscription.halted", // This is the event for paused subscriptions
  _SUBSCRIPTION_CANCELLED = "subscription.cancelled",
  _SUBSCRIPTION_COMPLETED = "subscription.completed",
  _SUBSCRIPTION_EXPIRED = "subscription.expired",
  _SUBSCRIPTION_UPDATED = "subscription.updated",

  // Payment events
  _PAYMENT_AUTHORIZED = "payment.authorized",
  _PAYMENT_CAPTURED = "payment.captured",
  _PAYMENT_FAILED = "payment.failed",

  // Invoice events
  _INVOICE_PAID = "invoice.paid",

  // Refund events
  _REFUND_CREATED = "refund.created",
  _REFUND_PROCESSED = "refund.processed",
  _REFUND_FAILED = "refund.failed"
}

// Subscription status values
export enum RazorpaySubscriptionStatus {
  _CREATED = "created",
  _AUTHENTICATED = "authenticated",
  _ACTIVE = "active",
  _PENDING = "pending",
  _HALTED = "halted", // This is the status for paused subscriptions
  _CANCELLED = "cancelled",
  _COMPLETED = "completed",
  _EXPIRED = "expired"
}

// Payment status values
export enum RazorpayPaymentStatus {
  _CREATED = "created",
  _AUTHORIZED = "authorized",
  _CAPTURED = "captured",
  _REFUNDED = "refunded",
  _FAILED = "failed"
}

// Refund status values
export enum RazorpayRefundStatus {
  _CREATED = "created",
  _PROCESSED = "processed",
  _FAILED = "failed"
}

// Supabase subscription status types (reused from existing implementation)
export enum SupabaseSubscriptionStatus {
  _ACTIVE = "active",
  _PENDING = "pending",
  _HALTED = "halted", // This is the status for paused subscriptions
  _CANCELLED = "cancelled",
  _COMPLETED = "completed",
  _EXPIRED = "expired",
  _PAYMENT_FAILED = "payment_failed",
  _AUTHENTICATED = "authenticated"
}

/**
 * Maps Razorpay subscription status to Supabase subscription status
 * @param razorpayStatus The Razorpay subscription status
 * @returns The corresponding Supabase subscription status
 */
export function mapRazorpayStatusToSupabase(razorpayStatus: string): SupabaseSubscriptionStatus {
  switch (razorpayStatus) {
    case RazorpaySubscriptionStatus._ACTIVE:
      return SupabaseSubscriptionStatus._ACTIVE;
    case RazorpaySubscriptionStatus._PENDING:
      return SupabaseSubscriptionStatus._PENDING;
    case RazorpaySubscriptionStatus._HALTED:
      return SupabaseSubscriptionStatus._HALTED;
    case RazorpaySubscriptionStatus._CANCELLED:
      return SupabaseSubscriptionStatus._CANCELLED;
    case RazorpaySubscriptionStatus._COMPLETED:
      return SupabaseSubscriptionStatus._COMPLETED;
    case RazorpaySubscriptionStatus._AUTHENTICATED:
      return SupabaseSubscriptionStatus._AUTHENTICATED;
    case RazorpaySubscriptionStatus._EXPIRED:
      return SupabaseSubscriptionStatus._EXPIRED;
    default:
      // Default to pending for unknown statuses
      console.warn(`Unknown Razorpay status: ${razorpayStatus}, defaulting to pending`);
      return SupabaseSubscriptionStatus._PENDING;
  }
}
